#!/usr/bin/env python3
"""
Simple Calendar Assistant - Direct integration without MCP complexity
"""

import os
import asyncio
from datetime import datetime, timedelta
from langchain_groq import ChatGroq
from dotenv import load_dotenv

load_dotenv()

# Set environment variables
os.environ['GOOGLE_CALENDAR_ID'] = 'primary'
os.environ['GOOGLE_TIME_ZONE'] = 'Europe/Vienna'
os.environ['GOOGLE_CREDENTIALS_PATH'] = 'C:/Users/<USER>/Documents/myprojectforwin-463421-708e786372d1.json'

from calendar_mcp_server import GoogleCalendarMCPServer

class CalendarAssistant:
    def __init__(self):
        self.model = ChatGroq(
            model="qwen/qwen3-32b",
            temperature=0.6,
            max_tokens=4096,
            api_key=os.getenv("GROQ_API_KEY")
        )
        self.calendar_server = None
    
    async def initialize(self):
        """Initialize the calendar server"""
        self.calendar_server = GoogleCalendarMCPServer()
        await self.calendar_server.initialize_calendar_service()
        print("✅ Calendar Assistant initialized")
    
    async def query_events(self, start_date, end_date, max_results=10):
        """Query calendar events"""
        class MockRequest:
            def __init__(self, name, arguments):
                self.params = type('obj', (object,), {'name': name, 'arguments': arguments})
        
        request = MockRequest("query_calendar_events", {
            "start_date": start_date,
            "end_date": end_date,
            "max_results": max_results
        })
        
        result = await self.calendar_server.call_tool(request)
        return result.content[0].text if result.content else "No events found"
    
    async def create_event(self, summary, start_datetime, end_datetime, description="", location=""):
        """Create a calendar event"""
        class MockRequest:
            def __init__(self, name, arguments):
                self.params = type('obj', (object,), {'name': name, 'arguments': arguments})
        
        request = MockRequest("create_calendar_event", {
            "summary": summary,
            "start_datetime": start_datetime,
            "end_datetime": end_datetime,
            "description": description,
            "location": location
        })
        
        result = await self.calendar_server.call_tool(request)
        return result.content[0].text if result.content else "Failed to create event"
    
    async def process_user_request(self, user_input):
        """Process user request and determine action"""
        # Simple keyword-based processing
        user_lower = user_input.lower()
        
        if any(word in user_lower for word in ['zeige', 'termine', 'events', 'kalender', 'heute', 'morgen', 'woche']):
            # Query events
            now = datetime.now()
            if 'heute' in user_lower:
                end_date = now.replace(hour=23, minute=59, second=59)
                start_date = now.replace(hour=0, minute=0, second=0)
            elif 'morgen' in user_lower:
                tomorrow = now + timedelta(days=1)
                start_date = tomorrow.replace(hour=0, minute=0, second=0)
                end_date = tomorrow.replace(hour=23, minute=59, second=59)
            else:
                start_date = now
                end_date = now + timedelta(days=7)
            
            result = await self.query_events(
                start_date.strftime("%Y-%m-%dT%H:%M:%S"),
                end_date.strftime("%Y-%m-%dT%H:%M:%S")
            )
            return f"📅 Deine Termine:\n{result}"
        
        elif any(word in user_lower for word in ['erstelle', 'create', 'neuer termin', 'meeting', 'termin']):
            # For now, create a simple test event
            now = datetime.now()
            start_time = now + timedelta(hours=1)
            end_time = start_time + timedelta(hours=1)
            
            result = await self.create_event(
                "Neuer Termin",
                start_time.strftime("%Y-%m-%dT%H:%M:%S"),
                end_time.strftime("%Y-%m-%dT%H:%M:%S"),
                "Automatisch erstellt vom Calendar Assistant",
                "Online"
            )
            return f"➕ Event erstellt:\n{result}"
        
        else:
            return "🤔 Ich kann dir helfen mit:\n- Termine anzeigen (z.B. 'Zeige mir meine Termine heute')\n- Termine erstellen (z.B. 'Erstelle einen Termin')"

async def main():
    """Main function"""
    assistant = CalendarAssistant()
    await assistant.initialize()
    
    print("🎯 Calendar Assistant gestartet!")
    print("Du kannst fragen wie:")
    print("- 'Zeige mir meine Termine heute'")
    print("- 'Erstelle einen neuen Termin'")
    print("- 'quit' zum Beenden")
    print("-" * 50)
    
    while True:
        user_input = input("\nDu: ")
        if user_input.lower() == "quit":
            print("👋 Auf Wiedersehen!")
            break
        
        try:
            response = await assistant.process_user_request(user_input)
            print(f"\nAssistant: {response}")
        except Exception as e:
            print(f"❌ Fehler: {e}")

if __name__ == "__main__":
    asyncio.run(main())
