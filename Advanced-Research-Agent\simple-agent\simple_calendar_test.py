#!/usr/bin/env python3
"""
Simple test of calendar functionality without MCP complexity
"""

import os
import asyncio
from datetime import datetime, timedelta

# Set environment variables
os.environ['GOOGLE_CALENDAR_ID'] = 'primary'
os.environ['GOOGLE_TIME_ZONE'] = 'Europe/Vienna'
os.environ['GOOGLE_CREDENTIALS_PATH'] = 'C:/Users/<USER>/Documents/myprojectforwin-463421-708e786372d1.json'

from calendar_mcp_server import GoogleCalendarMCPServer

async def test_calendar_directly():
    """Test calendar functionality directly"""
    print("🧪 Testing Calendar Functionality Directly...")
    
    # Initialize server
    server = GoogleCalendarMCPServer()
    await server.initialize_calendar_service()
    
    print("✅ Calendar service initialized")
    
    # Test query events
    print("\n📅 Testing event query...")
    now = datetime.now()
    end_date = now + timedelta(days=7)
    
    class MockParams:
        def __init__(self, name, arguments):
            self.name = name
            self.arguments = arguments
    
    class MockRequest:
        def __init__(self, name, arguments):
            self.params = MockParams(name, arguments)
    
    query_request = MockRequest(
        "query_calendar_events",
        {
            "start_date": now.strftime("%Y-%m-%dT%H:%M:%S"),
            "end_date": end_date.strftime("%Y-%m-%dT%H:%M:%S"),
            "max_results": 5
        }
    )
    
    result = await server.call_tool(query_request)
    print("Query result:")
    for content in result.content:
        print(f"   {content.text}")
    
    # Test create event
    print("\n➕ Testing event creation...")
    test_start = now + timedelta(hours=2)
    test_end = test_start + timedelta(hours=1)
    
    create_request = MockRequest(
        "create_calendar_event",
        {
            "summary": "🎯 Direct Test Event",
            "description": "This event was created by direct calendar test",
            "start_datetime": test_start.strftime("%Y-%m-%dT%H:%M:%S"),
            "end_datetime": test_end.strftime("%Y-%m-%dT%H:%M:%S"),
            "location": "Test Location Direct"
        }
    )
    
    create_result = await server.call_tool(create_request)
    print("Create result:")
    for content in create_result.content:
        print(f"   {content.text}")
    
    print("\n🎉 Direct calendar test completed!")
    print("\nThis shows that the calendar functionality works.")
    print("The MCP server integration might need some adjustments.")

if __name__ == "__main__":
    asyncio.run(test_calendar_directly())
