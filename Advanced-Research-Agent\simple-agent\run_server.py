#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to run the calendar MCP server with environment variables
"""

import os
import sys

# Set environment variables
os.environ['GOOGLE_CALENDAR_ID'] = 'primary'
os.environ['GOOGLE_TIME_ZONE'] = 'Europe/Vienna'
os.environ['GOOGLE_CREDENTIALS_PATH'] = 'C:/Users/<USER>/Documents/myprojectforwin-463421-708e786372d1.json'

# Import and run the server
from calendar_mcp_server import main
import asyncio

if __name__ == "__main__":
    print("🚀 Starting Google Calendar MCP Server...")
    print(f"📁 Credentials: {os.environ['GOOGLE_CREDENTIALS_PATH']}")
    print(f"📅 Calendar ID: {os.environ['GOOGLE_CALENDAR_ID']}")
    print(f"🌍 Timezone: {os.environ['GOOGLE_TIME_ZONE']}")
    print("-" * 50)
    
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Server stopped by user")
    except Exception as e:
        print(f"❌ Server error: {e}")
        sys.exit(1)
