#!/usr/bin/env python3
"""
Test script for the Google Calendar MCP Server
"""

import asyncio
import os
import sys
from datetime import datetime, timedelta

# Add the current directory to the path so we can import our server
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from calendar_mcp_server import GoogleCalendarMCPServer


async def test_calendar_server():
    """Test the calendar server functionality"""
    print("🧪 Testing Google Calendar MCP Server...")
    
    # Set up environment variables for testing
    os.environ['GOOGLE_CALENDAR_ID'] = 'primary'
    os.environ['GOOGLE_TIME_ZONE'] = 'Europe/Vienna'
    os.environ['GOOGLE_CREDENTIALS_PATH'] = 'C:/Users/<USER>/Documents/myprojectforwin-463421-708e786372d1.json'
    
    # Initialize server
    server = GoogleCalendarMCPServer()
    
    try:
        # Test initialization
        print("1. Testing server initialization...")
        await server.initialize_calendar_service()
        print("✅ Server initialized successfully")
        
        # Test listing tools
        print("\n2. Testing tool listing...")
        from mcp.types import ListToolsRequest
        tools = await server.list_tools(ListToolsRequest(method="tools/list"))
        print(f"✅ Found {len(tools)} tools:")
        for tool in tools:
            print(f"   - {tool.name}: {tool.description}")
        
        # Test querying events (next 7 days)
        print("\n3. Testing event query...")
        now = datetime.now()
        end_date = now + timedelta(days=7)
        
        # Create a simple mock request object for testing
        class MockParams:
            def __init__(self, name, arguments):
                self.name = name
                self.arguments = arguments

        class MockRequest:
            def __init__(self, name, arguments):
                self.params = MockParams(name, arguments)

        query_request = MockRequest(
            "query_calendar_events",
            {
                "start_date": now.strftime("%Y-%m-%dT%H:%M:%S"),
                "end_date": end_date.strftime("%Y-%m-%dT%H:%M:%S"),
                "max_results": 5
            }
        )
        
        result = await server.call_tool(query_request)
        print("✅ Query result:")
        for content in result.content:
            print(f"   {content.text}")
        
        # Test creating a test event
        print("\n4. Testing event creation...")
        test_start = now + timedelta(hours=1)
        test_end = test_start + timedelta(hours=1)
        
        create_request = MockRequest(
            "create_calendar_event",
            {
                "summary": "🧪 MCP Test Event",
                "description": "This is a test event created by the MCP server",
                "start_datetime": test_start.strftime("%Y-%m-%dT%H:%M:%S"),
                "end_datetime": test_end.strftime("%Y-%m-%dT%H:%M:%S"),
                "location": "Test Location"
            }
        )
        
        create_result = await server.call_tool(create_request)
        print("✅ Create result:")
        for content in create_result.content:
            print(f"   {content.text}")
        
        print("\n🎉 All tests completed successfully!")
        print("\nYour local MCP server is ready to use!")
        print("\nTo use it with your main script:")
        print("1. Install dependencies: pip install -r requirements.txt")
        print("2. Run your main script: python main.py")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        print("\nPlease check:")
        print("1. Your Google credentials file exists and is valid")
        print("2. The service account has Calendar API access")
        print("3. The calendar ID is correct")
        return False
    
    return True


if __name__ == "__main__":
    success = asyncio.run(test_calendar_server())
    sys.exit(0 if success else 1)
