#!/usr/bin/env python3
"""
Local MCP Server for Google Calendar Integration with OAuth 2.0
Provides tools for querying and managing Google Calendar events
"""

import asyncio
import json
import logging
import os
import sys
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Sequence

import pytz
from dateutil import parser
from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import InstalledAppFlow
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError

from mcp.server import Server
from mcp.server.models import InitializationOptions
from mcp.server.stdio import stdio_server
from mcp.types import (
    CallToolRequest,
    CallToolResult,
    ListToolsRequest,
    TextContent,
    Tool,
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# OAuth 2.0 scopes for Google Calendar
SCOPES = ['https://www.googleapis.com/auth/calendar']

class GoogleCalendarOAuthMCPServer:
    def __init__(self):
        self.server = Server("google-calendar-oauth-mcp")
        self.calendar_service = None
        self.calendar_id = None
        self.timezone = None
        self.credentials = None
        
        # Setup handlers
        self.server.list_tools = self.list_tools
        self.server.call_tool = self.call_tool
        
    def get_credentials(self):
        """Get OAuth 2.0 credentials for Google Calendar API"""
        creds = None
        token_file = 'token.json'
        credentials_file = os.getenv('GOOGLE_OAUTH_CREDENTIALS_PATH', 'credentials.json')
        
        # Load existing token if available
        if os.path.exists(token_file):
            creds = Credentials.from_authorized_user_file(token_file, SCOPES)
        
        # If there are no (valid) credentials available, let the user log in
        if not creds or not creds.valid:
            if creds and creds.expired and creds.refresh_token:
                try:
                    creds.refresh(Request())
                    logger.info("OAuth credentials refreshed successfully")
                except Exception as e:
                    logger.warning(f"Failed to refresh credentials: {e}")
                    creds = None
            
            if not creds:
                if not os.path.exists(credentials_file):
                    raise FileNotFoundError(
                        f"OAuth credentials file not found: {credentials_file}\n"
                        "Please download the OAuth 2.0 credentials from Google Cloud Console"
                    )
                
                flow = InstalledAppFlow.from_client_secrets_file(credentials_file, SCOPES)
                creds = flow.run_local_server(port=0)
                logger.info("OAuth authorization completed successfully")
            
            # Save the credentials for the next run
            with open(token_file, 'w') as token:
                token.write(creds.to_json())
                logger.info("OAuth token saved to token.json")
        
        return creds
    
    async def initialize_calendar_service(self):
        """Initialize Google Calendar service with OAuth credentials"""
        try:
            self.calendar_id = os.getenv('GOOGLE_CALENDAR_ID', 'primary')
            self.timezone = os.getenv('GOOGLE_TIME_ZONE', 'UTC')
            
            # Get OAuth credentials
            self.credentials = self.get_credentials()
            
            # Build the Calendar service
            self.calendar_service = build('calendar', 'v3', credentials=self.credentials)
            logger.info("Google Calendar service initialized successfully with OAuth")
            
        except Exception as e:
            logger.error(f"Failed to initialize Google Calendar service: {e}")
            raise
    
    async def list_tools(self, request: ListToolsRequest) -> List[Tool]:
        """List available calendar tools"""
        return [
            Tool(
                name="query_calendar_events",
                description="Query calendar events within a specified date range",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "start_date": {
                            "type": "string",
                            "description": "Start date in ISO format (YYYY-MM-DD) or datetime (YYYY-MM-DDTHH:MM:SS)"
                        },
                        "end_date": {
                            "type": "string", 
                            "description": "End date in ISO format (YYYY-MM-DD) or datetime (YYYY-MM-DDTHH:MM:SS)"
                        },
                        "max_results": {
                            "type": "integer",
                            "description": "Maximum number of events to return (default: 10)",
                            "default": 10
                        },
                        "calendar_id": {
                            "type": "string",
                            "description": "Calendar ID to query (default: primary calendar)",
                            "default": "primary"
                        }
                    },
                    "required": ["start_date", "end_date"]
                }
            ),
            Tool(
                name="create_calendar_event",
                description="Create a new calendar event",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "summary": {
                            "type": "string",
                            "description": "Event title/summary"
                        },
                        "description": {
                            "type": "string",
                            "description": "Event description (optional)"
                        },
                        "start_datetime": {
                            "type": "string",
                            "description": "Start datetime in ISO format (YYYY-MM-DDTHH:MM:SS)"
                        },
                        "end_datetime": {
                            "type": "string",
                            "description": "End datetime in ISO format (YYYY-MM-DDTHH:MM:SS)"
                        },
                        "location": {
                            "type": "string",
                            "description": "Event location (optional)"
                        },
                        "attendees": {
                            "type": "array",
                            "items": {"type": "string"},
                            "description": "List of attendee email addresses (optional)"
                        },
                        "calendar_id": {
                            "type": "string",
                            "description": "Calendar ID to create event in (default: primary calendar)",
                            "default": "primary"
                        }
                    },
                    "required": ["summary", "start_datetime", "end_datetime"]
                }
            ),
            Tool(
                name="update_calendar_event",
                description="Update an existing calendar event",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "event_id": {
                            "type": "string",
                            "description": "ID of the event to update"
                        },
                        "summary": {
                            "type": "string",
                            "description": "Event title/summary (optional)"
                        },
                        "description": {
                            "type": "string",
                            "description": "Event description (optional)"
                        },
                        "start_datetime": {
                            "type": "string",
                            "description": "Start datetime in ISO format (optional)"
                        },
                        "end_datetime": {
                            "type": "string",
                            "description": "End datetime in ISO format (optional)"
                        },
                        "location": {
                            "type": "string",
                            "description": "Event location (optional)"
                        },
                        "calendar_id": {
                            "type": "string",
                            "description": "Calendar ID (default: primary calendar)",
                            "default": "primary"
                        }
                    },
                    "required": ["event_id"]
                }
            ),
            Tool(
                name="delete_calendar_event",
                description="Delete a calendar event",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "event_id": {
                            "type": "string",
                            "description": "ID of the event to delete"
                        },
                        "calendar_id": {
                            "type": "string",
                            "description": "Calendar ID (default: primary calendar)",
                            "default": "primary"
                        }
                    },
                    "required": ["event_id"]
                }
            )
        ]

    async def call_tool(self, request: CallToolRequest) -> CallToolResult:
        """Handle tool calls"""
        try:
            if request.params.name == "query_calendar_events":
                return await self._query_calendar_events(request.params.arguments)
            elif request.params.name == "create_calendar_event":
                return await self._create_calendar_event(request.params.arguments)
            elif request.params.name == "update_calendar_event":
                return await self._update_calendar_event(request.params.arguments)
            elif request.params.name == "delete_calendar_event":
                return await self._delete_calendar_event(request.params.arguments)
            else:
                raise ValueError(f"Unknown tool: {request.params.name}")
        except Exception as e:
            logger.error(f"Error calling tool {request.params.name}: {e}")
            return CallToolResult(
                content=[TextContent(type="text", text=f"Error: {str(e)}")],
                isError=True
            )

    def _parse_datetime(self, dt_string: str) -> datetime:
        """Parse datetime string and ensure timezone awareness"""
        try:
            dt = parser.parse(dt_string)
            if dt.tzinfo is None:
                # If no timezone info, assume the configured timezone
                tz = pytz.timezone(self.timezone)
                dt = tz.localize(dt)
            return dt
        except Exception as e:
            raise ValueError(f"Invalid datetime format: {dt_string}. Use ISO format like '2024-01-01T10:00:00'")

    def _format_event(self, event: Dict[str, Any]) -> Dict[str, Any]:
        """Format event data for display"""
        formatted = {
            'id': event.get('id'),
            'summary': event.get('summary', 'No Title'),
            'description': event.get('description', ''),
            'location': event.get('location', ''),
            'status': event.get('status'),
            'created': event.get('created'),
            'updated': event.get('updated'),
            'creator': event.get('creator', {}).get('email', ''),
            'organizer': event.get('organizer', {}).get('email', ''),
        }

        # Handle start/end times
        start = event.get('start', {})
        end = event.get('end', {})

        if 'dateTime' in start:
            formatted['start'] = start['dateTime']
            formatted['end'] = end['dateTime']
            formatted['all_day'] = False
        elif 'date' in start:
            formatted['start'] = start['date']
            formatted['end'] = end['date']
            formatted['all_day'] = True

        # Handle attendees
        attendees = event.get('attendees', [])
        formatted['attendees'] = [
            {
                'email': attendee.get('email'),
                'responseStatus': attendee.get('responseStatus'),
                'displayName': attendee.get('displayName')
            }
            for attendee in attendees
        ]

        return formatted

    async def _query_calendar_events(self, arguments: Dict[str, Any]) -> CallToolResult:
        """Query calendar events within a date range"""
        try:
            start_date = arguments['start_date']
            end_date = arguments['end_date']
            max_results = arguments.get('max_results', 10)
            calendar_id = arguments.get('calendar_id', self.calendar_id or 'primary')

            # Parse and format dates
            start_dt = self._parse_datetime(start_date)
            end_dt = self._parse_datetime(end_date)

            # Query events
            events_result = self.calendar_service.events().list(
                calendarId=calendar_id,
                timeMin=start_dt.isoformat(),
                timeMax=end_dt.isoformat(),
                maxResults=max_results,
                singleEvents=True,
                orderBy='startTime'
            ).execute()

            events = events_result.get('items', [])

            if not events:
                return CallToolResult(
                    content=[TextContent(type="text", text="No events found in the specified date range.")]
                )

            # Format events for display
            formatted_events = [self._format_event(event) for event in events]

            result_text = f"Found {len(formatted_events)} events:\n\n"
            for event in formatted_events:
                result_text += f"📅 **{event['summary']}**\n"
                result_text += f"   ID: {event['id']}\n"
                result_text += f"   Start: {event['start']}\n"
                result_text += f"   End: {event['end']}\n"
                if event['location']:
                    result_text += f"   Location: {event['location']}\n"
                if event['description']:
                    result_text += f"   Description: {event['description']}\n"
                if event['attendees']:
                    attendee_emails = [a['email'] for a in event['attendees'] if a['email']]
                    if attendee_emails:
                        result_text += f"   Attendees: {', '.join(attendee_emails)}\n"
                result_text += "\n"

            return CallToolResult(
                content=[TextContent(type="text", text=result_text)]
            )

        except HttpError as e:
            error_msg = f"Google Calendar API error: {e}"
            logger.error(error_msg)
            return CallToolResult(
                content=[TextContent(type="text", text=error_msg)],
                isError=True
            )
        except Exception as e:
            error_msg = f"Error querying calendar events: {e}"
            logger.error(error_msg)
            return CallToolResult(
                content=[TextContent(type="text", text=error_msg)],
                isError=True
            )

    async def _create_calendar_event(self, arguments: Dict[str, Any]) -> CallToolResult:
        """Create a new calendar event"""
        try:
            summary = arguments['summary']
            start_datetime = arguments['start_datetime']
            end_datetime = arguments['end_datetime']
            description = arguments.get('description', '')
            location = arguments.get('location', '')
            attendees = arguments.get('attendees', [])
            calendar_id = arguments.get('calendar_id', self.calendar_id or 'primary')

            # Parse datetimes
            start_dt = self._parse_datetime(start_datetime)
            end_dt = self._parse_datetime(end_datetime)

            # Create event object
            event = {
                'summary': summary,
                'description': description,
                'location': location,
                'start': {
                    'dateTime': start_dt.isoformat(),
                    'timeZone': self.timezone,
                },
                'end': {
                    'dateTime': end_dt.isoformat(),
                    'timeZone': self.timezone,
                },
            }

            # Add attendees if provided
            if attendees:
                event['attendees'] = [{'email': email} for email in attendees]

            # Create the event
            created_event = self.calendar_service.events().insert(
                calendarId=calendar_id,
                body=event
            ).execute()

            result_text = f"✅ Event created successfully!\n\n"
            result_text += f"📅 **{created_event['summary']}**\n"
            result_text += f"   ID: {created_event['id']}\n"
            result_text += f"   Start: {created_event['start']['dateTime']}\n"
            result_text += f"   End: {created_event['end']['dateTime']}\n"
            if location:
                result_text += f"   Location: {location}\n"
            if description:
                result_text += f"   Description: {description}\n"
            if attendees:
                result_text += f"   Attendees: {', '.join(attendees)}\n"
            result_text += f"   Calendar Link: {created_event.get('htmlLink', 'N/A')}\n"

            return CallToolResult(
                content=[TextContent(type="text", text=result_text)]
            )

        except HttpError as e:
            error_msg = f"Google Calendar API error: {e}"
            logger.error(error_msg)
            return CallToolResult(
                content=[TextContent(type="text", text=error_msg)],
                isError=True
            )
        except Exception as e:
            error_msg = f"Error creating calendar event: {e}"
            logger.error(error_msg)
            return CallToolResult(
                content=[TextContent(type="text", text=error_msg)],
                isError=True
            )

    async def _update_calendar_event(self, arguments: Dict[str, Any]) -> CallToolResult:
        """Update an existing calendar event"""
        try:
            event_id = arguments['event_id']
            calendar_id = arguments.get('calendar_id', self.calendar_id or 'primary')

            # Get the existing event first
            existing_event = self.calendar_service.events().get(
                calendarId=calendar_id,
                eventId=event_id
            ).execute()

            # Update fields if provided
            if 'summary' in arguments:
                existing_event['summary'] = arguments['summary']
            if 'description' in arguments:
                existing_event['description'] = arguments['description']
            if 'location' in arguments:
                existing_event['location'] = arguments['location']

            if 'start_datetime' in arguments:
                start_dt = self._parse_datetime(arguments['start_datetime'])
                existing_event['start'] = {
                    'dateTime': start_dt.isoformat(),
                    'timeZone': self.timezone,
                }

            if 'end_datetime' in arguments:
                end_dt = self._parse_datetime(arguments['end_datetime'])
                existing_event['end'] = {
                    'dateTime': end_dt.isoformat(),
                    'timeZone': self.timezone,
                }

            # Update the event
            updated_event = self.calendar_service.events().update(
                calendarId=calendar_id,
                eventId=event_id,
                body=existing_event
            ).execute()

            result_text = f"✅ Event updated successfully!\n\n"
            result_text += f"📅 **{updated_event['summary']}**\n"
            result_text += f"   ID: {updated_event['id']}\n"
            result_text += f"   Start: {updated_event['start'].get('dateTime', updated_event['start'].get('date'))}\n"
            result_text += f"   End: {updated_event['end'].get('dateTime', updated_event['end'].get('date'))}\n"
            if updated_event.get('location'):
                result_text += f"   Location: {updated_event['location']}\n"
            if updated_event.get('description'):
                result_text += f"   Description: {updated_event['description']}\n"
            result_text += f"   Last Updated: {updated_event.get('updated')}\n"

            return CallToolResult(
                content=[TextContent(type="text", text=result_text)]
            )

        except HttpError as e:
            error_msg = f"Google Calendar API error: {e}"
            logger.error(error_msg)
            return CallToolResult(
                content=[TextContent(type="text", text=error_msg)],
                isError=True
            )
        except Exception as e:
            error_msg = f"Error updating calendar event: {e}"
            logger.error(error_msg)
            return CallToolResult(
                content=[TextContent(type="text", text=error_msg)],
                isError=True
            )

    async def _delete_calendar_event(self, arguments: Dict[str, Any]) -> CallToolResult:
        """Delete a calendar event"""
        try:
            event_id = arguments['event_id']
            calendar_id = arguments.get('calendar_id', self.calendar_id or 'primary')

            # Get event details before deletion for confirmation
            try:
                event = self.calendar_service.events().get(
                    calendarId=calendar_id,
                    eventId=event_id
                ).execute()
                event_summary = event.get('summary', 'Untitled Event')
            except HttpError:
                event_summary = f"Event ID: {event_id}"

            # Delete the event
            self.calendar_service.events().delete(
                calendarId=calendar_id,
                eventId=event_id
            ).execute()

            result_text = f"🗑️ Event deleted successfully!\n\n"
            result_text += f"Deleted event: **{event_summary}**\n"
            result_text += f"Event ID: {event_id}\n"

            return CallToolResult(
                content=[TextContent(type="text", text=result_text)]
            )

        except HttpError as e:
            if e.resp.status == 404:
                error_msg = f"Event not found: {event_id}"
            else:
                error_msg = f"Google Calendar API error: {e}"
            logger.error(error_msg)
            return CallToolResult(
                content=[TextContent(type="text", text=error_msg)],
                isError=True
            )
        except Exception as e:
            error_msg = f"Error deleting calendar event: {e}"
            logger.error(error_msg)
            return CallToolResult(
                content=[TextContent(type="text", text=error_msg)],
                isError=True
            )


async def main():
    """Main function to run the MCP server"""
    # Initialize the server
    server_instance = GoogleCalendarOAuthMCPServer()

    try:
        # Initialize Google Calendar service
        await server_instance.initialize_calendar_service()
        logger.info("OAuth MCP Server ready to accept connections")

        # Run the server
        async with stdio_server() as streams:
            await server_instance.server.run(*streams, InitializationOptions())

    except Exception as e:
        logger.error(f"Server error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
