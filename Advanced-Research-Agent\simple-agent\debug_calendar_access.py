#!/usr/bin/env python3
"""
Debug calendar access - find the correct calendar ID
"""

import os
from google.oauth2 import service_account
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError

def debug_calendar_access():
    """Debug what calendars the service account can access"""
    print("🔍 Debugging Calendar Access...")
    
    credentials_path = "C:/Users/<USER>/Documents/myprojectforwin-463421-708e786372d1.json"
    
    try:
        # Load service account credentials
        credentials = service_account.Credentials.from_service_account_file(
            credentials_path,
            scopes=['https://www.googleapis.com/auth/calendar']
        )
        
        # Build Calendar service
        service = build('calendar', 'v3', credentials=credentials)
        print("✅ Service account loaded successfully")
        
        # Test different calendar IDs
        test_calendar_ids = [
            'primary',
            '<EMAIL>',
            '<EMAIL>'
        ]
        
        for calendar_id in test_calendar_ids:
            print(f"\n🧪 Testing calendar ID: {calendar_id}")
            try:
                # Try to get calendar info
                calendar_info = service.calendars().get(calendarId=calendar_id).execute()
                print(f"✅ Calendar found: {calendar_info.get('summary', 'No name')}")
                
                # Try to list events
                events_result = service.events().list(
                    calendarId=calendar_id,
                    maxResults=1
                ).execute()
                
                events = events_result.get('items', [])
                print(f"✅ Can access events: {len(events)} events found")
                
            except HttpError as e:
                print(f"❌ Error: {e.resp.status} - {e.resp.reason}")
                if e.resp.status == 404:
                    print("   Calendar not found or no access")
                elif e.resp.status == 403:
                    print("   Access forbidden - check permissions")
        
        # Try to list all accessible calendars
        print(f"\n📋 Trying to list all accessible calendars...")
        try:
            calendar_list = service.calendarList().list().execute()
            calendars = calendar_list.get('items', [])
            
            if calendars:
                print(f"✅ Found {len(calendars)} accessible calendars:")
                for calendar in calendars:
                    print(f"   - {calendar['summary']} (ID: {calendar['id']})")
                    print(f"     Access: {calendar.get('accessRole', 'unknown')}")
            else:
                print("❌ No calendars accessible to this service account")
                
        except HttpError as e:
            print(f"❌ Cannot list calendars: {e}")
        
    except Exception as e:
        print(f"❌ Failed to initialize service: {e}")

if __name__ == "__main__":
    debug_calendar_access()
