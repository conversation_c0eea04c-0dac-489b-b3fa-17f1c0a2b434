from langchain_groq import <PERSON>tGroq
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client
from langchain_mcp_adapters.tools import load_mcp_tools
from langgraph.prebuilt import create_react_agent
from dotenv import load_dotenv
import asyncio
import os

load_dotenv()



model = ChatGroq(
    model="qwen/qwen3-32b",
    temperature=0.6,
    max_tokens=4096,
    api_key=os.getenv("GROQ_API_KEY")
)

server_params = StdioServerParameters(
    command="python",
    transport="stdio",
    args=["calendar_mcp_server_oauth.py"],
    env={
        "GOOGLE_CALENDAR_ID": "<EMAIL>",
        "GOOGLE_TIME_ZONE": "Europe/Vienna",
        "GOOGLE_OAUTH_CREDENTIALS_PATH": "credentials.json"
    },
    enabled="true"
)


async def main():
    async with stdio_client(server_params) as (read, write):
        async with Client<PERSON>ession(read, write) as session:
            await session.initialize()
            tools = await load_mcp_tools(session)
            agent = create_react_agent(model, tools)

            messages = [
                {
                    "role": "system",
                    "content": "You are a helpful assistant that can manage Google Calendar events. You can query existing events, create new events, update existing events, and delete events. Think step by step and use the appropriate calendar tools to help the user."
                }
            ]

            print("Available Tools -", *[tool.name for tool in tools])
            print("-" * 60)

            while True:
                user_input = input("\nYou: ")
                if user_input == "quit":
                    print("Goodbye")
                    break

                messages.append({"role": "user", "content": user_input[:175000]})

                try:
                    agent_response = await agent.ainvoke({"messages": messages})

                    ai_message = agent_response["messages"][-1].content
                    print("\nAgent:", ai_message)
                except Exception as e:
                    print("Error:", e)


if __name__ == "__main__":
    asyncio.run(main())