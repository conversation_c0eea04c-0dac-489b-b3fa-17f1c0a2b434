#!/usr/bin/env python3
"""
Test Service Account with domain-wide delegation (impersonation)
"""

import os
import sys
from google.oauth2 import service_account
from googleapiclient.discovery import build

def test_service_account_impersonation():
    """Test service account with user impersonation"""
    print("🧪 Testing Service Account with User Impersonation...")
    
    credentials_path = "C:/Users/<USER>/Documents/myprojectforwin-463421-708e786372d1.json"
    user_email = "<EMAIL>"
    
    try:
        # Load service account credentials
        credentials = service_account.Credentials.from_service_account_file(
            credentials_path,
            scopes=['https://www.googleapis.com/auth/calendar']
        )
        
        # Create delegated credentials (impersonate the user)
        delegated_credentials = credentials.with_subject(user_email)
        
        # Build Calendar service
        service = build('calendar', 'v3', credentials=delegated_credentials)
        
        # Test calendar access
        print("✅ Service account credentials loaded")
        print(f"✅ Impersonating user: {user_email}")
        
        # Try to list calendars
        calendar_list = service.calendarList().list().execute()
        calendars = calendar_list.get('items', [])
        
        print(f"✅ Found {len(calendars)} calendars:")
        for calendar in calendars:
            print(f"   - {calendar['summary']} (ID: {calendar['id']})")
        
        # Try to query events from primary calendar
        from datetime import datetime, timedelta
        now = datetime.now()
        end_date = now + timedelta(days=7)
        
        events_result = service.events().list(
            calendarId='primary',
            timeMin=now.isoformat() + 'Z',
            timeMax=end_date.isoformat() + 'Z',
            maxResults=5,
            singleEvents=True,
            orderBy='startTime'
        ).execute()
        
        events = events_result.get('items', [])
        print(f"✅ Found {len(events)} events in primary calendar")
        
        return True
        
    except Exception as e:
        print(f"❌ Service Account impersonation failed: {e}")
        print("\nThis means:")
        print("1. Domain-wide delegation is not enabled, OR")
        print("2. Service Account doesn't have the right permissions, OR") 
        print("3. This approach doesn't work with personal Gmail accounts")
        return False

if __name__ == "__main__":
    success = test_service_account_impersonation()
    if not success:
        print("\n💡 Recommendation: Use OAuth 2.0 instead of Service Account")
        print("   Service Accounts work best with G Suite/Google Workspace")
    sys.exit(0 if success else 1)
