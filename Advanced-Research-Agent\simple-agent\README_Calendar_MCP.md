# Google Calendar MCP Server

Ein lokaler MCP (Model Context Protocol) Server für Google Calendar Integration.

## Features

- 📅 **<PERSON><PERSON>der Events abfragen** - Events in einem bestimmten Zeitraum abrufen
- ➕ **Events erstellen** - Neue Kalender-Events mit allen Details hinzufügen
- ✏️ **Events bearbeiten** - Bestehende Events aktualisieren
- 🗑️ **Events löschen** - Events aus dem Kalender entfernen

## Setup

### 1. Abhängigkeiten installieren

```bash
pip install -r requirements.txt
```

### 2. Google Calendar API Setup

1. Gehe zur [Google Cloud Console](https://console.cloud.google.com/)
2. Erstelle ein neues Projekt oder wähle ein bestehendes aus
3. Aktiviere die Google Calendar API
4. Erstelle Service Account Credentials:
   - Gehe zu "APIs & Services" > "Credentials"
   - Klicke "Create Credentials" > "Service Account"
   - Lade die JSON-Datei herunter
5. Teile deinen Kalender mit der Service Account Email-Adresse

### 3. Umgebungsvariablen

Die folgenden Umgebungsvariablen werden benötigt:

- `GOOGLE_CREDENTIALS_PATH`: Pfad zur Service Account JSON-Datei
- `GOOGLE_CALENDAR_ID`: Deine Kalender-ID (meist deine Email-Adresse)
- `GOOGLE_TIME_ZONE`: Deine Zeitzone (z.B. "Europe/Vienna")

Diese sind bereits in `main.py` konfiguriert.

## Verwendung

### Standalone Server testen

```bash
python test_calendar_server.py
```

### Mit dem Hauptskript verwenden

```bash
python main.py
```

## Verfügbare Tools

### 1. query_calendar_events
Fragt Kalender-Events in einem bestimmten Zeitraum ab.

**Parameter:**
- `start_date` (required): Start-Datum im ISO-Format
- `end_date` (required): End-Datum im ISO-Format
- `max_results` (optional): Maximale Anzahl Events (Standard: 10)
- `calendar_id` (optional): Kalender-ID (Standard: primary)

### 2. create_calendar_event
Erstellt ein neues Kalender-Event.

**Parameter:**
- `summary` (required): Event-Titel
- `start_datetime` (required): Start-Zeit im ISO-Format
- `end_datetime` (required): End-Zeit im ISO-Format
- `description` (optional): Event-Beschreibung
- `location` (optional): Event-Ort
- `attendees` (optional): Liste von Email-Adressen
- `calendar_id` (optional): Kalender-ID

### 3. update_calendar_event
Aktualisiert ein bestehendes Event.

**Parameter:**
- `event_id` (required): ID des zu aktualisierenden Events
- `summary` (optional): Neuer Titel
- `description` (optional): Neue Beschreibung
- `start_datetime` (optional): Neue Start-Zeit
- `end_datetime` (optional): Neue End-Zeit
- `location` (optional): Neuer Ort
- `calendar_id` (optional): Kalender-ID

### 4. delete_calendar_event
Löscht ein Kalender-Event.

**Parameter:**
- `event_id` (required): ID des zu löschenden Events
- `calendar_id` (optional): Kalender-ID

## Beispiele

### Events der nächsten Woche abfragen
```
Zeige mir alle Termine der nächsten Woche
```

### Neues Meeting erstellen
```
Erstelle ein Meeting für morgen um 14:00 bis 15:00 mit dem Titel "Team Besprechung"
```

### Event bearbeiten
```
Ändere das Event mit ID "abc123" auf 16:00 Uhr
```

## Troubleshooting

### Häufige Probleme

1. **"Credentials file not found"**
   - Überprüfe den Pfad zur JSON-Datei
   - Stelle sicher, dass die Datei existiert

2. **"Calendar not found"**
   - Überprüfe die Kalender-ID
   - Stelle sicher, dass der Service Account Zugriff auf den Kalender hat

3. **"Permission denied"**
   - Teile deinen Kalender mit der Service Account Email
   - Gib dem Service Account "Make changes to events" Berechtigung

### Logs

Der Server loggt alle Aktivitäten. Bei Problemen schaue in die Konsolen-Ausgabe für detaillierte Fehlermeldungen.

## Architektur

- `calendar_mcp_server.py`: Haupt-MCP-Server Implementation
- `main.py`: Client-Anwendung die den MCP Server verwendet
- `test_calendar_server.py`: Test-Skript für den Server
- `requirements.txt`: Python-Abhängigkeiten
