# OAuth 2.0 Setup für Google Calendar

## Schritt 1: OAuth Credentials erstellen

1. **<PERSON><PERSON><PERSON> zur [Google Cloud Console](https://console.cloud.google.com/)**

2. **Wähle dein Projekt aus** (myprojectforwin-463421)

3. **<PERSON><PERSON><PERSON> zu "APIs & Services" > "Credentials"**

4. **<PERSON><PERSON><PERSON> "Create Credentials" > "OAuth 2.0 Client IDs"**

5. **Konfiguriere den OAuth Consent Screen** (falls noch nicht gemacht):
   - Application type: Desktop application
   - Name: "Calendar MCP Server"
   - User support email: deine Email
   - Developer contact: deine Email

6. **Erstelle OAuth 2.0 Client ID:**
   - Application type: **Desktop application**
   - Name: "Calendar MCP Desktop Client"

7. **Lade die JSON-Datei herunter** und benenne sie um zu `credentials.json`

8. **Platziere `credentials.json` in diesem Ordner**

## Schritt 2: OAuth Flow testen

Nach dem Setup kannst du den OAuth Flow testen:

```bash
python calendar_mcp_server_oauth.py
```

Dies wird:
1. Einen Browser öffnen für die Google-Anmeldung
2. Nach Berechtigung fragen für Calendar-Zugriff
3. Ein `token.json` File erstellen für zukünftige Verwendung

## Schritt 3: MCP Server verwenden

Nach der ersten Authentifizierung kannst du den Server normal verwenden:

```bash
python main_oauth.py
```

## Dateien die erstellt werden:

- `credentials.json` - OAuth Client Credentials (von Google heruntergeladen)
- `token.json` - Access/Refresh Token (automatisch erstellt nach Anmeldung)

## Sicherheit:

- `credentials.json` enthält Client-Secrets - nicht öffentlich teilen
- `token.json` enthält deine Access-Tokens - nicht öffentlich teilen
- Beide Dateien sind in `.gitignore` eingetragen (falls vorhanden)
