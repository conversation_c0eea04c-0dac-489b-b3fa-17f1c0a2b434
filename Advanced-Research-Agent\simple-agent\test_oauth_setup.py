#!/usr/bin/env python3
"""
Test script to setup and verify OAuth 2.0 for Google Calendar
"""

import os
import sys
import asyncio
from datetime import datetime, <PERSON><PERSON><PERSON>

def check_oauth_files():
    """Check if OAuth files exist"""
    print("🔍 Checking OAuth setup...")
    
    credentials_file = "credentials.json"
    token_file = "token.json"
    
    if not os.path.exists(credentials_file):
        print(f"❌ Missing: {credentials_file}")
        print("\n📋 To create OAuth credentials:")
        print("1. Go to https://console.cloud.google.com/")
        print("2. Select your project")
        print("3. Go to 'APIs & Services' > 'Credentials'")
        print("4. Click 'Create Credentials' > 'OAuth 2.0 Client IDs'")
        print("5. Choose 'Desktop application'")
        print("6. Download the JSON file and rename it to 'credentials.json'")
        print("7. Place it in this directory")
        return False
    else:
        print(f"✅ Found: {credentials_file}")
    
    if os.path.exists(token_file):
        print(f"✅ Found: {token_file} (OAuth token already exists)")
    else:
        print(f"ℹ️  Missing: {token_file} (will be created after first OAuth flow)")
    
    return True

async def test_oauth_flow():
    """Test the OAuth flow and calendar access"""
    print("\n🧪 Testing OAuth flow...")
    
    # Set environment variables
    os.environ['GOOGLE_CALENDAR_ID'] = 'primary'
    os.environ['GOOGLE_TIME_ZONE'] = 'Europe/Vienna'
    os.environ['GOOGLE_OAUTH_CREDENTIALS_PATH'] = 'credentials.json'
    
    try:
        # Import and test the OAuth server
        from calendar_mcp_server_oauth import GoogleCalendarOAuthMCPServer
        
        server = GoogleCalendarOAuthMCPServer()
        
        print("1. Initializing OAuth calendar service...")
        await server.initialize_calendar_service()
        print("✅ OAuth authentication successful!")
        
        print("\n2. Testing calendar access...")
        # Test a simple calendar query
        now = datetime.now()
        end_date = now + timedelta(days=7)
        
        # Create a mock request for testing
        class MockParams:
            def __init__(self, name, arguments):
                self.name = name
                self.arguments = arguments
        
        class MockRequest:
            def __init__(self, name, arguments):
                self.params = MockParams(name, arguments)
        
        query_request = MockRequest(
            "query_calendar_events",
            {
                "start_date": now.strftime("%Y-%m-%dT%H:%M:%S"),
                "end_date": end_date.strftime("%Y-%m-%dT%H:%M:%S"),
                "max_results": 5
            }
        )
        
        result = await server.call_tool(query_request)
        print("✅ Calendar query successful!")
        
        if result.content:
            print("\n📅 Calendar access test result:")
            for content in result.content:
                print(f"   {content.text}")
        
        print("\n🎉 OAuth setup completed successfully!")
        print("\nYou can now use:")
        print("   python main.py")
        
        return True
        
    except FileNotFoundError as e:
        print(f"❌ File not found: {e}")
        print("Please make sure credentials.json exists in this directory")
        return False
    except Exception as e:
        print(f"❌ OAuth test failed: {e}")
        print("\nThis might be normal if it's the first run.")
        print("The OAuth flow will open a browser window for authentication.")
        return False

async def main():
    """Main test function"""
    print("🚀 Google Calendar OAuth Setup Test")
    print("=" * 50)
    
    # Check files
    if not check_oauth_files():
        print("\n❌ Please create OAuth credentials first (see instructions above)")
        return False
    
    # Test OAuth flow
    success = await test_oauth_flow()
    
    if success:
        print("\n✅ All tests passed! OAuth is ready to use.")
    else:
        print("\n⚠️  OAuth setup needs completion. Follow the browser prompts if they appear.")
    
    return success

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
